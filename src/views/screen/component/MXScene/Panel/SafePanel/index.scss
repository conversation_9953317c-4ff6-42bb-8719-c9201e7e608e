.tech-panel {
    position: fixed;
    left: 20px;
    top: 100px;
    width: 360px;
    height: calc(100vh - 110px);
    z-index: 100;
    pointer-events: auto;
    color: #fff;
    transition: width 0.3s cubic-bezier(0.25, 0.8, 0.25, 1),
      transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

    // 添加折叠状态样式
    &.collapsed {
      width: 30px;
      transform: translateX(-10px);

      .panel-container {
        opacity: 0;
        visibility: hidden;
        transform: translateX(-20px);
      }

      .collapse-button {
        right: 20px;
      }
    }

    .panel-container {
      width: 100%;
      height: 100%;
      background: #011c46b3;
      border: 1px solid rgba(45, 115, 193, 0.6);
      border-radius: 2px;
      padding: 12px;
    //   display: flex;
      flex-direction: column;
      gap: 15px;
      overflow-y: auto;
      overflow-x: hidden;
      transition: opacity 0.3s cubic-bezier(0.25, 0.8, 0.25, 1),
        visibility 0.3s cubic-bezier(0.25, 0.8, 0.25, 1),
        transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(13, 47, 102, 0.3);
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(100, 181, 246, 0.5);
        border-radius: 2px;

        &:hover {
          background: rgba(100, 181, 246, 0.7);
        }
      }
    }

    // 折叠/展开按钮样式
    .collapse-button {
      position: absolute;
      top: 50%;
      right: -2px;
      transform: translateY(-50%);
      width: 22px;
      height: 38px;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
      z-index: 10;
      overflow: hidden;

      &:hover {
        transform: translateY(-50%) scale(1.05);

        img {
          filter: brightness(1.2);
        }
      }

      &:active {
        transform: translateY(-50%) scale(0.95);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
      }

      img {
        width: 22px;
        height: 38px;
        transition: transform 0.5s cubic-bezier(0.25, 0.8, 0.25, 1),
          filter 0.3s ease;
        transform: rotate(180deg);

        &.rotated {
          transform: rotate(0);
        }
      }
    }

    .panel-section {
      border-radius: 2px;
      padding: 8px;
      position: relative;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      background: rgba(17, 149, 255, 0.08);
      margin-bottom: 10px;
      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(
          90deg,
          rgba(45, 115, 193, 0) 0%,
          rgba(45, 115, 193, 0.3) 50%,
          rgba(45, 115, 193, 0) 100%
        );
      }

      &.top-section {
        height: 320px;
        // flex: 1;
      }

      &.middle-section {
        height: 320px;
      }

      &.bottom-section {
        height: 320px;
      }

      &.gate-safety-section {
        height: 320px;
      }

      &.safety-monitor-section {
        height: 320px;
      }

      // 天气信息样式
      .weather-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        height: 28px;

        .title {
          font-size: 16px;
          font-weight: 700;
          font-family: AlimamaShuHeiTi;
          color: #8cd2ff;
        }

        .time-select {
          display: flex;
          align-items: center;
          gap: 4px;
          height: 28px;

          :deep(.time-range-select) {
            width: 90px; // 设置固定宽度

            .el-input {
              .el-input__wrapper {
                background: transparent;
                box-shadow: none !important;
                padding: 0;
                height: 28px;

                &.is-focus {
                  box-shadow: none !important;
                }

                .el-input__inner {
                  color: #8cd2ff;
                  font-size: 13px;
                  height: 28px;
                  line-height: 28px;
                  padding: 0;
                  border: none;
                  background: transparent;
                  text-align: center;

                  &::placeholder {
                    color: rgba(100, 181, 246, 0.8);
                  }
                }

                .el-select__caret {
                  color: #8cd2ff;
                  font-size: 12px;
                  height: 28px;
                  line-height: 28px;
                  width: 12px;
                }
              }
            }
          }
        }
      }

      .line {
        display: flex;
        justify-content: space-between;
        width: 100%;
        height: 2px;

        .left,
        .right {
          /* flex:0 0 100px; */
          width: 10px;
          height: 2px;
          background-color: #3276b1;
        }

        .center {
          flex: 1;
          height: 2px;
          background-color: #19477a;
        }
      }

      .weather-main {
        display: flex;
        margin-bottom: 0;
        padding: 0;
        height: 85px;

        .current-weather {
          width: 110px;
          text-align: center;
          position: relative;
          z-index: 2;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .rain-info {
            z-index: 2;
            text-align: left;
          }
        }

        .rain-label {
          color: rgba(255, 255, 255, 0.85);
          margin-bottom: 2px;
          font-weight: 500;
          letter-spacing: 0.5px;
          text-transform: uppercase;
          // font-size: 10px;
          font-size: 12px;
        }

        .rain-value {
          margin-top: 0;

          .value {
            font-size: 20px;
            font-weight: bold;
            color: #29b6f6;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            letter-spacing: 0.5px;
          }

          .unit {
            font-size: 12px;
            margin-left: 2px;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 300;
          }
        }

        .update-time {
          font-size: 10px;
          color: rgba(255, 255, 255, 0.6);
          margin-top: 4px;
          display: flex;
          align-items: center;

          .update-icon {
            width: 10px;
            height: 10px;
            margin-right: 3px;
          }

          span {
            transform: translateY(1px);
          }
        }

        .day-forecast {
          flex: 1;
          display: flex;
          justify-content: space-around;
          padding-left: 10px;
          gap: 4px;

          .forecast-item {
            flex: 1;
            text-align: center;
            padding: 3px 1px;
            position: relative;
            z-index: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;

            &:not(:last-child)::after {
              content: "";
              position: absolute;
              right: -2px;
              top: 20%;
              height: 60%;
              width: 1px;
              background: rgba(255, 255, 255, 0.15);
            }

            &:hover {
              transform: translateY(-2px);

              .weather-icon-img {
                transform: scale(1.1);
              }
            }

            .day-badge {
              font-size: 9px;
              font-weight: 600;
              color: #fff;
              background: rgba(21, 101, 192, 0.3);
              border-radius: 8px;
              padding: 1px 6px;
              display: inline-block;
              text-transform: uppercase;
              letter-spacing: 0.5px;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
              margin-bottom: 4px;
            }

            .weather-icon-container {
              position: relative;
              width: 28px;
              height: 28px;
              margin: 2px auto;

              .weather-icon-img {
                width: 100%;
                height: 100%;
                object-fit: contain;
                position: relative;
                z-index: 1;
                filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.15));
                transition: transform 0.3s ease;
              }
            }

            .temp {
              font-size: 14px;
              font-weight: 700;
              color: #29b6f6;
              text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
              margin-top: 2px;
            }
          }
        }
      }

      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
        position: relative;
        height: 28px;

        .title {
          font-size: 16px;
          font-weight: 700;
          font-family: AlimamaShuHeiTi;
          color: #8cd2ff;
        }

        .time-select {
          display: flex;
          align-items: center;
          gap: 4px;
          height: 28px;

          :deep(.time-range-select) {
            width: 90px; // 设置固定宽度

            .el-input {
              .el-input__wrapper {
                background: transparent;
                box-shadow: none !important;
                padding: 0;
                height: 28px;

                &.is-focus {
                  box-shadow: none !important;
                }

                .el-input__inner {
                  color: #8cd2ff;
                  font-size: 13px;
                  height: 28px;
                  line-height: 28px;
                  padding: 0;
                  border: none;
                  background: transparent;
                  text-align: center;

                  &::placeholder {
                    color: rgba(100, 181, 246, 0.8);
                  }
                }

                .el-select__caret {
                  color: #8cd2ff;
                  font-size: 12px;
                  height: 28px;
                  line-height: 28px;
                  width: 12px;
                }
              }
            }
          }
        }

        .location-info {
          display: flex;
          align-items: center;
          gap: 4px;
          height: 28px;
        }

        .gate-select {
          display: flex;
          align-items: center;
          gap: 4px;
          height: 28px;

          :deep(.gate-select-dropdown) {
            width: 80px;

            .el-input {
              .el-input__wrapper {
                background: transparent;
                box-shadow: none !important;
                padding: 0;
                height: 28px;

                &.is-focus {
                  box-shadow: none !important;
                }

                .el-input__inner {
                  color: #8cd2ff;
                  font-size: 13px;
                  height: 28px;
                  line-height: 28px;
                  padding: 0;
                  border: none;
                  background: transparent;
                  text-align: center;

                  &::placeholder {
                    color: rgba(100, 181, 246, 0.8);
                  }
                }

                .el-select__caret {
                  color: #8cd2ff;
                  font-size: 12px;
                  height: 28px;
                  line-height: 28px;
                  width: 12px;
                }
              }
            }
          }
        }
      }

      .section-content {
        position: relative;
        flex: 1;
        // overflow: visible;

        .chart-container {
          height: 100%;
          width: 100%;
          margin-top: 0;
        }

        .river-chart-container {
          height: 200px;
          min-height: 200px;
          margin-top: 10px;
          width: 100%;
        }

        .safety-analysis-container {
          height: 100%;
          width: 100%;

          .chart-container {
            width: 100%;
            height: 100%;
            min-height: 200px;
          }
        }

        .gate-safety-container {
          height: 100%;
          width: 100%;
          padding: 10px 0;
        }

        .safety-monitor-container {
          height: 100%;
          width: 100%;
          padding: 10px 0;
          display: flex;
          flex-direction: column;
          gap: 15px;

          .monitor-type-selector {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: 0 10px;

            :deep(.el-button-group) {
              .el-button {
                background: rgba(13, 47, 102, 0.3);
                border: 1px solid rgba(45, 115, 193, 0.5);
                color: rgba(255, 255, 255, 0.8);
                font-size: 12px;
                padding: 6px 12px;
                height: 28px;
                line-height: 1;

                &:hover {
                  background: rgba(21, 101, 192, 0.2);
                  border-color: rgba(100, 181, 246, 0.6);
                  color: #64b5f6;
                }

                &.el-button--primary {
                  background: rgba(21, 101, 192, 0.4);
                  border-color: rgba(100, 181, 246, 0.8);
                  color: #64b5f6;
                  box-shadow: 0 0 8px rgba(100, 181, 246, 0.3);

                  &:hover {
                    background: rgba(21, 101, 192, 0.5);
                    border-color: rgba(100, 181, 246, 1);
                    box-shadow: 0 0 12px rgba(100, 181, 246, 0.4);
                  }
                }
              }
            }
          }

          .monitor-table-container {
            flex: 1;
            overflow: hidden;
            padding: 0 10px;

            :deep(.monitor-data-table) {
              --el-table-header-bg-color: rgba(25, 118, 210, 0.4);
              --el-table-bg-color: transparent;
              --el-table-tr-bg-color: transparent;
              --el-table-header-text-color: rgba(255, 255, 255, 0.95);
              --el-table-text-color: rgba(255, 255, 255, 0.9);
              --el-table-border-color: rgba(45, 115, 193, 0.3);
              --el-table-row-hover-bg-color: rgba(21, 101, 192, 0.1);

              background-color: transparent;
              border: 1px solid rgba(45, 115, 193, 0.3);
              border-radius: 4px;

              .el-table__header {
                th.el-table__cell {
                  background-color: rgba(25, 118, 210, 0.4) !important;
                  border-bottom: 1px solid rgba(45, 115, 193, 0.3);
                  color: rgba(255, 255, 255, 0.95);
                  font-size: 12px;
                  font-weight: 600;
                  padding: 8px 0;

                  .cell {
                    color: rgba(255, 255, 255, 0.95);
                  }
                }
              }

              .el-table__body {
                tr {
                  background-color: transparent;

                  td.el-table__cell {
                    background-color: transparent !important;
                    border-bottom: 1px solid rgba(45, 115, 193, 0.2);
                    color: rgba(255, 255, 255, 0.9);
                    font-size: 12px;
                    padding: 6px 0;

                    .cell {
                      color: rgba(255, 255, 255, 0.9);
                    }
                  }

                  &:hover > td.el-table__cell {
                    background-color: rgba(21, 101, 192, 0.1) !important;
                  }

                  &.el-table__row--striped > td.el-table__cell {
                    background-color: rgba(13, 47, 102, 0.1) !important;
                  }
                }
              }

              &::before {
                display: none;
              }

              .el-table__inner-wrapper::before {
                display: none;
              }

              .el-table__body-wrapper {
                &::-webkit-scrollbar {
                  width: 4px;
                }

                &::-webkit-scrollbar-track {
                  background: rgba(13, 47, 102, 0.3);
                  border-radius: 2px;
                }

                &::-webkit-scrollbar-thumb {
                  background: rgba(100, 181, 246, 0.5);
                  border-radius: 2px;

                  &:hover {
                    background: rgba(100, 181, 246, 0.7);
                  }
                }
              }
            }
          }
        }

        .gate-circles {
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          align-items: center;
          height: 100%;
          gap: 15px;
        }

        .gate-row {
          display: flex;
          justify-content: space-around;
          align-items: center;
          width: 100%;
          gap: 8px;

          &.first-row {
            flex: 1;
            justify-content: space-around;
          }

          &.second-row {
            flex: 1;
            justify-content: center;
            gap: 40px;
          }
        }

        .gate-circle {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          transition: transform 0.3s ease;
          cursor: pointer;

          &:hover {
            transform: translateY(-2px);

            .circle-ring {
              border-color: rgba(100, 181, 246, 0.9);
              box-shadow:
                inset 0 0 10px rgba(0, 0, 0, 0.3),
                0 0 15px rgba(100, 181, 246, 0.4);
            }

            .water-ball {
              box-shadow:
                inset 0 2px 4px rgba(255, 255, 255, 0.4),
                0 4px 8px rgba(0, 0, 0, 0.4),
                inset 0 -2px 4px rgba(0, 0, 0, 0.3);
            }
          }
        }

        .circle-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 8px;
        }

        .circle-ring {
          width: 66px;
          height: 66px;
          border-radius: 50%;
          border: 2px solid rgba(100, 181, 246, 0.6);
          background: rgba(13, 47, 102, 0.3);
          position: relative;
          overflow: hidden;
          display: flex;
          align-items: flex-end;
          justify-content: center;
          box-shadow:
            inset 0 0 10px rgba(0, 0, 0, 0.3),
            0 0 10px rgba(100, 181, 246, 0.2);
        }

        .water-ball {
          width: 100%;
          background: linear-gradient(
            to bottom,
            rgba(100, 181, 246, 0.9),
            rgba(33, 150, 243, 1),
            rgba(21, 101, 192, 0.8)
          );
          border-radius: 50% 50% 0 0;
          position: relative;
          transition: height 0.8s cubic-bezier(0.4, 0, 0.2, 1);
          box-shadow:
            inset 0 2px 4px rgba(255, 255, 255, 0.3),
            0 2px 4px rgba(0, 0, 0, 0.3),
            inset 0 -2px 4px rgba(0, 0, 0, 0.2);

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 30%;
            background: linear-gradient(
              to bottom,
              rgba(255, 255, 255, 0.2),
              transparent
            );
            border-radius: 50% 50% 0 0;
            pointer-events: none;
          }
        }

        .water-ripples {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          pointer-events: none;
        }

        .ripple {
          position: absolute;
          border: 1px solid rgba(255, 255, 255, 0.4);
          border-radius: 50%;
          animation: ripple-animation 3s infinite;
        }

        .ripple-1 {
          width: 20px;
          height: 20px;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          animation-delay: 0s;
        }

        .ripple-2 {
          width: 30px;
          height: 30px;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          animation-delay: 1s;
        }

        .ripple-3 {
          width: 40px;
          height: 40px;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          animation-delay: 2s;
        }

        @keyframes ripple-animation {
          0% {
            opacity: 0.6;
            transform: translate(-50%, -50%) scale(0.3);
          }
          30% {
            opacity: 0.8;
            transform: translate(-50%, -50%) scale(0.8);
          }
          70% {
            opacity: 0.4;
            transform: translate(-50%, -50%) scale(1.4);
          }
          100% {
            opacity: 0;
            transform: translate(-50%, -50%) scale(2.0);
          }
        }

        .water-level-text {
          position: absolute;
          top: 35%;
          left: 50%;
          transform: translate(-50%, -50%);
          color: #ffffff;
          font-size: 13px;
          font-weight: bold;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
          z-index: 2;
        }

        .risk-level-text {
          position: absolute;
          top: 65%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 11px;
          font-weight: bold;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
          z-index: 2;
          white-space: nowrap;

          &.risk-low {
            color: #4CAF50;
          }

          &.risk-medium {
            color: #FF9800;
          }

          &.risk-high {
            color: #FF5722;
          }

          &.risk-danger {
            color: #F44336;
          }
        }

        .gate-label {
          color: rgba(255, 255, 255, 0.9);
          font-size: 12px;
          font-weight: 500;
          text-align: center;
          margin-top: 4px;
        }

        .update-time-bottom {
          text-align: center;
          font-size: 12px;
          color: rgba(255, 255, 255, 0.7);
          margin-top: 5px;
          padding-bottom: 5px;
        }
      }

      // 统一天气卡片样式
      .unified-weather-card {
        background: linear-gradient(
          135deg,
          rgba(21, 101, 192, 0.2),
          rgba(3, 169, 244, 0.05)
        );
        border-radius: 12px;
        margin: 6px 0;
        padding: 8px 10px;
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1),
          inset 0 -1px 3px rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(100, 181, 246, 0.3);
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(5px);
      }
    }

    // Element Plus表格样式覆盖
    :deep(.rain-data-table) {
      --el-table-header-bg-color: transparent;
      --el-table-bg-color: transparent;
      --el-table-tr-bg-color: transparent;
      --el-table-header-text-color: #fff;
      --el-table-text-color: #fff;

      background-color: transparent;
      color: #fff;
      height: 100%;
      width: 100%;

      .el-table__header {
        background-color: transparent;

        th.el-table__cell {
          background-color: rgba(25, 118, 210, 0.4) !important;
          border-bottom: 1px solid rgba(45, 115, 193, 0.3);
          color: rgba(255, 255, 255, 0.95);
          font-size: 12px;

          .cell {
            color: rgba(255, 255, 255, 0.95);
          }
        }
      }

      .el-table__body {
        background-color: transparent;

        tr {
          background-color: transparent;

          td {
            background-color: transparent;
            color: #fff;

            .cell {
              color: #fff;
            }
          }
        }
      }

      &::before {
        display: none;
      }

      .el-table__inner-wrapper::before {
        display: none;
      }

      .el-table__header-wrapper {
        background-color: transparent;

        th.el-table__cell {
          background-color: rgba(25, 118, 210, 0.4) !important;
          border-bottom: 1px solid rgba(45, 115, 193, 0.3);

          &.is-leaf {
            border-bottom: 1px solid rgba(45, 115, 193, 0.3);
          }
        }
      }

      .el-table__body-wrapper {
        &::-webkit-scrollbar {
          width: 4px;
        }

        &::-webkit-scrollbar-thumb {
          background-color: rgba(33, 150, 243, 0.5);
          border-radius: 2px;
        }

        tr.rain-table-row:hover > td.el-table__cell {
          background-color: rgba(21, 101, 192, 0.1);
        }

        td.el-table__cell {
          background-color: transparent !important;
          border-bottom: 1px solid rgba(45, 115, 193, 0.2);
        }
      }
    }

    // 保留原有的排名样式
    .rank-item {
      width: 24px;
      height: 18px;
      line-height: 18px;
      margin: 0 auto;
      transform: skew(-15deg);
      position: relative;
      overflow: hidden;

      &.rank-1 {
        background: linear-gradient(45deg, #ff4757, #ff6b81);
        color: #fff;
        box-shadow: 0 2px 4px rgba(255, 71, 87, 0.3);
      }

      &.rank-2 {
        background: linear-gradient(45deg, #ffa502, #ffb733);
        color: #fff;
        box-shadow: 0 2px 4px rgba(255, 165, 2, 0.3);
      }

      &.rank-3 {
        background: linear-gradient(45deg, #2ed573, #7bed9f);
        color: #fff;
        box-shadow: 0 2px 4px rgba(46, 213, 115, 0.3);
      }
    }

    .empty-data-container {
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 20px 0;
      height: 100%;
      width: 100%;

      .empty-icon {
        font-size: 40px;
        opacity: 0.9;
        margin-bottom: 12px;

        .el-icon {
          width: 48px;
          height: 48px;
          font-size: 48px;
          color: rgba(100, 181, 246, 0.6);
          transition: color 0.3s ease;
        }
      }

      .empty-text {
        font-size: 14px;
        font-weight: 500;
        color: rgba(200, 220, 255, 0.6);
        letter-spacing: 0.5px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        transition: color 0.3s ease;
        text-align: center;
        max-width: 80%;
      }
    }
  }

  :deep(.basin-select),
  :deep(.river-select) {
    width: 80px; // 设置固定宽度

    .el-input {
      .el-input__wrapper {
        background: transparent;
        box-shadow: none !important;
        padding: 0;
        height: 28px;

        &.is-focus {
          box-shadow: none !important;
        }

        .el-input__inner {
          color: #8cd2ff;
          font-size: 13px;
          height: 28px;
          line-height: 28px;
          padding: 0;
          border: none;
          background: transparent;
          text-align: center;

          &::placeholder {
            color: rgba(100, 181, 246, 0.8);
          }
        }

        .el-select__caret {
          color: #8cd2ff;
          font-size: 12px;
          height: 28px;
          line-height: 28px;
          width: 12px;
        }

        &:hover {
          background: rgba(21, 101, 192, 0.1);
        }

        &.is-focus {
          background: rgba(21, 101, 192, 0.15);
        }
      }
    }
  }
    // 安全预警样式
    .safety-warning-container {
      padding: 20px;

      .warning-indicators {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 15px;
        margin-bottom: 20px;

        .warning-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 8px;
          flex: 1;

          .warning-level {
            font-size: 14px;
            font-weight: bold;
            padding: 6px 8px;
            border-radius: 6px;
            min-width: 40px;
            text-align: center;

            &.level-1 {
              color: #ff4757;
              background: rgba(255, 71, 87, 0.1);
              border: 1px solid rgba(255, 71, 87, 0.3);
            }

            &.level-2 {
              color: #ffa502;
              background: rgba(255, 165, 2, 0.1);
              border: 1px solid rgba(255, 165, 2, 0.3);
            }

            &.level-3 {
              color: #ffdd59;
              background: rgba(255, 221, 89, 0.1);
              border: 1px solid rgba(255, 221, 89, 0.3);
            }

            &.level-4 {
              color: #2ed573;
              background: rgba(46, 213, 115, 0.1);
              border: 1px solid rgba(46, 213, 115, 0.3);
            }
          }

          .warning-count {
            font-size: 20px;
            font-weight: bold;
            color: #ffffff;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
          }
        }
      }

      .warning-scroll-container {
        height: 120px;
        overflow: hidden;
        position: relative;
        border: 1px solid rgba(255, 71, 87, 0.3);
        border-radius: 8px;
        background: rgba(255, 71, 87, 0.05);

        .marquee-wrapper {
          height: 100%;

          .warning-message {
            height: 30px;
            line-height: 30px;
            padding: 0 15px;
            color: #ff4757;
            font-size: 14px;
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            border-bottom: 1px solid rgba(255, 71, 87, 0.1);

            &:last-child {
              border-bottom: none;
            }

            &:hover {
              background: rgba(255, 71, 87, 0.1);
            }
          }
        }
      }
    }


  // 将下拉菜单样式移到全局作用域
  .el-popper.is-pure.dark-select-dropdown {
    background: rgba(14, 74, 131, 0.95) !important;
    border: 1px solid rgba(45, 115, 193, 0.5) !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;

    .el-select-dropdown__wrap {
      max-height: 200px;
    }

    .el-select-dropdown__list {
      padding: 4px 0;
      background: transparent !important;
    }

    .el-select-dropdown__item,
    .el-tree,
    .el-tree-node__content {
      color: rgba(255, 255, 255, 0.9) !important;
      padding: 0 8px;
      height: 28px;
      line-height: 28px;
      font-size: 13px;
      background: transparent !important;

      &.hover,
      &:hover {
        background: rgba(21, 101, 192, 0.2) !important;
      }

      &.selected {
        background: rgba(21, 101, 192, 0.3) !important;
        color: #64b5f6 !important;
        font-weight: normal;

        &.hover,
        &:hover {
          background: rgba(21, 101, 192, 0.4) !important;
        }
      }
    }

    .el-scrollbar__view {
      padding: 4px 0;
    }

    .el-scrollbar__bar {
      background: transparent;
      width: 4px;
      right: 2px;

      &.is-vertical {
        .el-scrollbar__thumb {
          background: rgba(100, 181, 246, 0.3);
          border-radius: 2px;

          &:hover {
            background: rgba(100, 181, 246, 0.5);
          }
        }
      }
    }

    .el-popper__arrow {
      display: none !important;
    }
  }