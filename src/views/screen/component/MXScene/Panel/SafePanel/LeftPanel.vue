<!--
 * @Description:
 * @Author: liguiyuan
 * @LastEditors: liguiyuan
-->
<template>
    <div class="tech-panel" :class="{ collapsed: isPanelCollapsed }">
      <div class="panel-container">
        <!-- 上部区域 - 安全预警 -->
        <div class="panel-section top-section">
          <div class="weather-header">
            <div class="title">安全预警</div>
          </div>
          <div class="line">
            <div class="left"></div>
            <div class="center"></div>
            <div class="right"></div>
          </div>
          <div class="safety-warning-container">
            <!-- 预警指标行 -->
            <div class="warning-indicators">
              <div class="warning-item">
                <div class="warning-level level-1">Ⅰ级</div>
                <div class="warning-count">{{ warningData.level1 }}</div>
              </div>
              <div class="warning-item">
                <div class="warning-level level-2">Ⅱ级</div>
                <div class="warning-count">{{ warningData.level2 }}</div>
              </div>
              <div class="warning-item">
                <div class="warning-level level-3">Ⅲ级</div>
                <div class="warning-count">{{ warningData.level3 }}</div>
              </div>
              <div class="warning-item">
                <div class="warning-level level-4">Ⅳ级</div>
                <div class="warning-count">{{ warningData.level4 }}</div>
              </div>
            </div>

            <!-- 滚动预报信息 -->
            <div class="warning-scroll-container">
              <Vue3Marquee
                :duration="30"
                direction="up"
                :pauseOnHover="true"
                class="marquee-wrapper"
              >
                <div
                  v-for="(message, index) in warningMessages"
                  :key="index"
                  class="warning-message"
                >
                  {{ message }}
                </div>
              </Vue3Marquee>
            </div>
          </div>
        </div>

        <!-- 下部区域 - 安全分析 -->
        <div class="panel-section bottom-section">
          <div class="section-header">
            <div class="title">安全分析</div>
            <div class="time-select">
              <el-select
                v-model="selectedTimeRange"
                placeholder="选择时间"
                class="time-range-select"
                popper-class="dark-select-dropdown"
                @change="updateSafetyChart"
              >
                <el-option
                  v-for="option in timeRangeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </div>
          </div>
          <div class="line">
            <div class="left"></div>
            <div class="center"></div>
            <div class="right"></div>
          </div>
          <div class="section-content">
            <div class="safety-analysis-container">
              <div class="chart-container" ref="safetyChartRef"></div>
            </div>
          </div>
        </div>

        <!-- 安全监测区域 -->
        <div class="panel-section safety-monitor-section">
          <div class="section-header">
            <div class="title">安全监测</div>
            <div class="gate-select">
              <el-select
                v-model="selectedGate"
                placeholder="选择闸门"
                class="gate-select-dropdown"
                popper-class="dark-select-dropdown"
              >
                <el-option
                  v-for="gate in gateOptions"
                  :key="gate.value"
                  :label="gate.label"
                  :value="gate.value"
                />
              </el-select>
            </div>
          </div>
          <div class="line">
            <div class="left"></div>
            <div class="center"></div>
            <div class="right"></div>
          </div>
          <div class="section-content">
            <div class="safety-monitor-container">
              <!-- 监测类型选择按钮组 -->
              <div class="monitor-type-selector">
                <el-button-group>
                  <el-button
                    :type="monitorType === 'seepage' ? 'primary' : 'default'"
                    @click="monitorType = 'seepage'"
                    size="small"
                  >
                    渗压
                  </el-button>
                  <el-button
                    :type="monitorType === 'temperature' ? 'primary' : 'default'"
                    @click="monitorType = 'temperature'"
                    size="small"
                  >
                    温度
                  </el-button>
                </el-button-group>
              </div>

              <!-- 监测数据表格 -->
              <div class="monitor-table-container">
                <el-table
                  :data="currentMonitorData"
                  class="monitor-data-table"
                  :max-height="200"
                  :min-height="150"
                  stripe
                >
                  <el-table-column
                    prop="device"
                    label="监测设备"
                    width="120"
                    align="center"
                  />
                  <el-table-column
                    :prop="monitorType === 'seepage' ? 'seepageValue' : 'temperatureValue'"
                    :label="monitorType === 'seepage' ? '渗压(Kpa)' : '温度(°)'"
                    align="center"
                  />
                </el-table>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 添加折叠/展开按钮 -->
      <div class="collapse-button" @click="togglePanel">
        <img
          :src="rightIcon"
          alt="折叠/展开"
          :class="{ rotated: isPanelCollapsed }"
        />
      </div>
    </div>
  </template>

  <script setup>
  import { storeToRefs } from "pinia";
  import * as echarts from "echarts";
  import rightIcon from "@/views/screen/image/right.png";
  import useScreenStore from "@/store/modules/screen";
  import { Vue3Marquee } from 'vue3-marquee';

  defineOptions({
    name: "TechPanel",
  });

  const screenStore = useScreenStore();
  const { isPanelCollapsed } = storeToRefs(screenStore);

  // 图表引用
  const safetyChartRef = ref(null);
  let safetyChart = null;



  // 使用 store 中的状态和方法
  const togglePanel = () => {
    screenStore.togglePanel();
  };

  // 安全预警数据
  const warningData = ref({
    level1: 3,  // 一级预警数量
    level2: 7,  // 二级预警数量
    level3: 12, // 三级预警数量
    level4: 5   // 四级预警数量
  });

  // 滚动预警信息
  const warningMessages = ref([
    "1#闸左岸位移计P15温度Ⅳ级预警",
    "2#闸右岸渗压计P08压力Ⅲ级预警",
    "3#闸上游水位监测点H12超限Ⅱ级预警",
    "4#闸下游流量计F05异常Ⅰ级预警",
    "5#闸闸门开度传感器G03故障Ⅳ级预警",
    "1#闸下游水质监测点W08超标Ⅲ级预警",
    "2#闸上游泥沙含量S15异常Ⅱ级预警",
    "3#闸左岸边坡位移D22超限Ⅰ级预警",
    "4#闸右岸渗流监测点L09异常Ⅳ级预警",
    "5#闸闸室温度传感器T12超温Ⅲ级预警"
  ]);





  // 安全监测相关数据
  const selectedGate = ref('1'); // 默认选择一号闸
  const monitorType = ref('seepage'); // 默认选择渗压

  // 闸门选项
  const gateOptions = ref([
    { value: '1', label: '一号闸' },
    { value: '2', label: '二号闸' },
    { value: '3', label: '三号闸' },
    { value: '4', label: '四号闸' },
    { value: '5', label: '五号闸' }
  ]);

  // 监测数据 - Mock数据
  const monitorTableData = ref([
    { device: '渗压计P01', seepageValue: '2.35', temperatureValue: '18.5' },
    { device: '渗压计P02', seepageValue: '2.42', temperatureValue: '19.2' },
    { device: '渗压计P03', seepageValue: '2.18', temperatureValue: '17.8' },
    { device: '渗压计P04', seepageValue: '2.56', temperatureValue: '20.1' },
    { device: '渗压计P05', seepageValue: '2.31', temperatureValue: '18.9' },
    { device: '渗压计P06', seepageValue: '2.48', temperatureValue: '19.6' },
    { device: '渗压计P07', seepageValue: '2.22', temperatureValue: '17.5' },
    { device: '渗压计P08', seepageValue: '2.39', temperatureValue: '18.7' },
    { device: '渗压计P09', seepageValue: '2.45', temperatureValue: '19.8' },
    { device: '渗压计P10', seepageValue: '2.28', temperatureValue: '18.2' }
  ]);

  // 温度监测设备数据
  const temperatureMonitorData = ref([
    { device: '温度传感器T01', seepageValue: '2.35', temperatureValue: '18.5' },
    { device: '热电偶TC02', seepageValue: '2.42', temperatureValue: '19.2' },
    { device: '温度计T03', seepageValue: '2.18', temperatureValue: '17.8' },
    { device: '热敏电阻RT04', seepageValue: '2.56', temperatureValue: '20.1' },
    { device: '温度传感器T05', seepageValue: '2.31', temperatureValue: '18.9' },
    { device: '热电偶TC06', seepageValue: '2.48', temperatureValue: '19.6' },
    { device: '温度计T07', seepageValue: '2.22', temperatureValue: '17.5' },
    { device: '热敏电阻RT08', seepageValue: '2.39', temperatureValue: '18.7' },
    { device: '温度传感器T09', seepageValue: '2.45', temperatureValue: '19.8' },
    { device: '热电偶TC10', seepageValue: '2.28', temperatureValue: '18.2' }
  ]);

  // 计算属性：根据监测类型返回对应的数据
  const currentMonitorData = computed(() => {
    return monitorType.value === 'temperature' ? temperatureMonitorData.value : monitorTableData.value;
  });

  // 安全分析相关数据
  const selectedTimeRange = ref('7'); // 默认选择最近7日
  const timeRangeOptions = ref([
    { value: '7', label: '最近7日' },
    { value: '15', label: '最近15日' },
    { value: '30', label: '最近30日' }
  ]);

  // 安全分析图表数据
  const safetyAnalysisData = ref({
    '7': {
      dates: ['07-01', '07-02', '07-03', '07-04', '07-05', '07-06', '07-07'],
      seepagePressure: [2.8, 3.1, 2.9, 3.3, 3.0, 3.5, 3.2],
      upperWarning: [4.5, 4.6, 4.4, 4.7, 4.5, 4.8, 4.6],
      lowerWarning: [1.5, 1.4, 1.6, 1.3, 1.5, 1.2, 1.4]
    },
    '15': {
      dates: ['06-23', '06-24', '06-25', '06-26', '06-27', '06-28', '06-29', '06-30', '07-01', '07-02', '07-03', '07-04', '07-05', '07-06', '07-07'],
      seepagePressure: [2.5, 2.7, 2.9, 3.0, 2.8, 3.2, 3.1, 3.0, 2.8, 3.1, 2.9, 3.3, 3.0, 3.5, 3.2],
      upperWarning: [4.3, 4.4, 4.5, 4.6, 4.4, 4.7, 4.5, 4.6, 4.5, 4.6, 4.4, 4.7, 4.5, 4.8, 4.6],
      lowerWarning: [1.7, 1.6, 1.5, 1.4, 1.6, 1.3, 1.5, 1.4, 1.5, 1.4, 1.6, 1.3, 1.5, 1.2, 1.4]
    },
    '30': {
      dates: ['06-08', '06-09', '06-10', '06-11', '06-12', '06-13', '06-14', '06-15', '06-16', '06-17', '06-18', '06-19', '06-20', '06-21', '06-22', '06-23', '06-24', '06-25', '06-26', '06-27', '06-28', '06-29', '06-30', '07-01', '07-02', '07-03', '07-04', '07-05', '07-06', '07-07'],
      seepagePressure: [2.2, 2.4, 2.6, 2.5, 2.7, 2.6, 2.8, 2.7, 2.6, 2.7, 2.8, 2.6, 2.9, 2.7, 2.8, 2.5, 2.7, 2.9, 3.0, 2.8, 3.2, 3.1, 3.0, 2.8, 3.1, 2.9, 3.3, 3.0, 3.5, 3.2],
      upperWarning: [4.2, 4.3, 4.4, 4.3, 4.5, 4.4, 4.6, 4.5, 4.4, 4.5, 4.6, 4.4, 4.7, 4.5, 4.6, 4.3, 4.4, 4.5, 4.6, 4.4, 4.7, 4.5, 4.6, 4.5, 4.6, 4.4, 4.7, 4.5, 4.8, 4.6],
      lowerWarning: [1.8, 1.7, 1.6, 1.7, 1.5, 1.6, 1.4, 1.5, 1.6, 1.5, 1.4, 1.6, 1.3, 1.5, 1.4, 1.7, 1.6, 1.5, 1.4, 1.6, 1.3, 1.5, 1.4, 1.5, 1.4, 1.6, 1.3, 1.5, 1.2, 1.4]
    }
  });



  // 初始化安全分析图表
  const initSafetyChart = () => {
    if (!safetyChartRef.value) return;

    const container = safetyChartRef.value;
    safetyChart = echarts.init(container);

    updateSafetyChart();

    // 确保图表正确调整大小
    setTimeout(() => {
      safetyChart && safetyChart.resize();
    }, 100);
  };

  // 更新安全分析图表
  const updateSafetyChart = () => {
    if (!safetyChart) return;

    const currentData = safetyAnalysisData.value[selectedTimeRange.value];

    const option = {
      backgroundColor: 'transparent',
      grid: {
        top: 40,
        left: 10,
        right: 10,
        bottom: 10,
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        confine: true,
        backgroundColor: 'rgba(13, 47, 102, 0.9)',
        borderColor: 'rgba(45, 115, 193, 0.5)',
        borderWidth: 1,
        padding: [8, 12],
        textStyle: {
          color: '#fff',
          fontSize: 12
        },
        formatter: function(params) {
          let result = `<div style="font-size: 11px; color: rgba(255,255,255,0.7);">${params[0].axisValue}</div>`;

          params.forEach((param) => {
            const color = param.color;
            const value = param.value;
            const marker = `<span style="display:inline-block;margin-right:4px;border-radius:50%;width:6px;height:6px;background:${color};"></span>`;
            result += `<div style="margin-top: 4px; font-size: 12px; display: flex; justify-content: space-between; align-items: center;">
              <span style="display: flex; align-items: center;">${marker}${param.seriesName}</span>
              <span style="font-weight:bold;margin-left:12px;">${value}</span>
            </div>`;
          });
          return result;
        }
      },
      legend: {
        data: ['渗压', '上警戒线', '下警戒线'],
        textStyle: {
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: 11
        },
        top: 10,
        right: 15
      },
      xAxis: {
        type: 'category',
        data: currentData.dates,
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisTick: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.7)',
          fontSize: 10,
          margin: 8
        }
      },
      yAxis: {
        type: 'value',
        name: '渗压值(Kpa)',
        nameTextStyle: {
          color: 'rgba(255, 255, 255, 0.7)',
          fontSize: 10,
          padding: [0, 0, 0, 10]
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisTick: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.7)',
          fontSize: 10,
          margin: 8
        }
      },
      series: [
        {
          name: '渗压',
          type: 'line',
          data: currentData.seepagePressure,
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: '#64B5F6'
          },
          lineStyle: {
            width: 3,
            color: '#64B5F6'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(100, 181, 246, 0.3)' },
              { offset: 1, color: 'rgba(100, 181, 246, 0.1)' }
            ])
          }
        },
        {
          name: '上警戒线',
          type: 'line',
          data: currentData.upperWarning,
          smooth: true,
          symbol: 'none',
          lineStyle: {
            width: 2,
            color: '#FF6B6B',
            type: 'dashed'
          },
          itemStyle: {
            color: '#FF6B6B'
          }
        },
        {
          name: '下警戒线',
          type: 'line',
          data: currentData.lowerWarning,
          smooth: true,
          symbol: 'none',
          lineStyle: {
            width: 2,
            color: '#FFA500',
            type: 'dashed'
          },
          itemStyle: {
            color: '#FFA500'
          }
        }
      ]
    };

    safetyChart.setOption(option);
  };

  // 监听窗口大小变化，重新调整图表大小
  const handleResize = () => {
    if (safetyChart) {
      safetyChart.resize();
    }
  };



  onMounted(() => {
    // 初始化安全分析图表
    nextTick(() => {
      setTimeout(() => {
        initSafetyChart();
      }, 200);
    });

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize);
  });

  onUnmounted(() => {
    // 清理图表
    if (safetyChart) {
      safetyChart.dispose();
    }

    // 移除窗口大小变化监听
    window.removeEventListener('resize', handleResize);
  });
  </script>

  <style lang="scss" scoped>
  @use "./index.scss";
  </style>
