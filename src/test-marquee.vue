<template>
  <div class="test-container">
    <h2>Vue3Marquee 测试</h2>
    
    <!-- 垂直滚动测试 -->
    <div class="test-section">
      <h3>垂直滚动测试</h3>
      <div class="warning-scroll-container">
        <Vue3Marquee
          :duration="30"
          direction="up"
          :pauseOnHover="true"
          class="marquee-wrapper"
        >
          <div 
            v-for="(message, index) in warningMessages" 
            :key="index"
            class="warning-message"
          >
            {{ message }}
          </div>
        </Vue3Marquee>
      </div>
    </div>

    <!-- 水平滚动测试 -->
    <div class="test-section">
      <h3>水平滚动测试</h3>
      <div class="horizontal-scroll-container">
        <Vue3Marquee
          :duration="20"
          direction="normal"
          :pauseOnHover="true"
        >
          <span v-for="(message, index) in warningMessages" :key="index" class="horizontal-message">
            {{ message }}
          </span>
        </Vue3Marquee>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { Vue3Marquee } from 'vue3-marquee';

const warningMessages = ref([
  "1#闸左岸渗流监测点L01异常Ⅲ级预警",
  "2#闸闸室温度传感器T05超温Ⅱ级预警", 
  "3#闸右岸位移监测点D03位移异常Ⅳ级预警",
  "4#闸左岸渗流监测点L02异常Ⅲ级预警",
  "5#闸闸室温度传感器T06超温Ⅱ级预警",
  "6#闸右岸位移监测点D04位移异常Ⅳ级预警",
  "7#闸左岸渗流监测点L03异常Ⅲ级预警",
  "8#闸闸室温度传感器T07超温Ⅱ级预警"
]);
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;
}

.warning-scroll-container {
  height: 120px;
  overflow: hidden;
  position: relative;
  border: 1px solid rgba(255, 71, 87, 0.3);
  border-radius: 8px;
  background: rgba(255, 71, 87, 0.05);
}

.marquee-wrapper {
  height: 100%;
}

.warning-message {
  height: 30px;
  line-height: 30px;
  padding: 0 15px;
  color: #ff4757;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-bottom: 1px solid rgba(255, 71, 87, 0.1);
}

.warning-message:last-child {
  border-bottom: none;
}

.warning-message:hover {
  background: rgba(255, 71, 87, 0.1);
}

.horizontal-scroll-container {
  height: 50px;
  overflow: hidden;
  border: 1px solid rgba(45, 115, 193, 0.3);
  border-radius: 8px;
  background: rgba(45, 115, 193, 0.05);
  display: flex;
  align-items: center;
}

.horizontal-message {
  color: #2d73c1;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  margin-right: 50px;
}
</style>
